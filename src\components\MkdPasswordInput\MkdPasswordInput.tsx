import { useState } from "react";
import { MkdInput } from "@/components/MkdInput";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";

interface MkdPasswordInputProps {
  register?: any;
  errors?: any;
  name?: string;
  label?: string;
  className?: string;
  inputClassName?: string;
  labelClassName?: string;
  required?: boolean;
  disabled?: boolean;
}

const MkdPasswordInput = ({
  register,
  errors,
  name = "password",
  label = "Password",
  className,
  inputClassName,
  labelClassName,
  required = true,
  disabled = false,
}: MkdPasswordInputProps) => {
  const [type, setType] = useState("password");

  return (
    <div>
      <label
        className={`mb-2 block cursor-pointer text-[.875rem] font-bold ${labelClassName}`}
        htmlFor={name}
      >
        {label}
        {required && (
          <sup className="z-[99999] text-[.825rem] text-red-600">*</sup>
        )}
        {/* {StringCaser(label, { casetype: "capitalize", separator: "space" })} */}
      </label>
      <div
        className={`relative p-0 focus:shadow-outline font-inter leading-tight text-black shadow focus:outline-none focus:ring-0 focus:shadow-outline font-inter h-[3rem] flex w-full appearance-none items-center rounded-[.625rem] border border-border bg-input font-inter text-text hover:border-border-hover focus-within:border-primary transition-colors duration-200 ${className}`}
      >
        <div className="grow">
          <MkdInput
            type={type}
            name={name}
            disabled={disabled}
            className={`${inputClassName} !bg-input w-full !m-0 !border-0 !shadow-none`}
            errors={errors}
            register={register}
          />
        </div>
        <div className="absolute inset-x-0 right-2 left-auto h-[1.5rem] max-h-[1.5rem] min-h-[1.5rem] w-[1.5rem] min-w-[1.5rem] max-w-[1.5rem]">
          {type === "password" ? (
            <AiOutlineEye
              className="h-[1.5rem] max-h-[1.5rem] min-h-[1.5rem] w-[1.5rem] min-w-[1.5rem] max-w-[1.5rem] cursor-pointer text-icon hover:text-icon-hover transition-colors duration-200"
              onClick={() => setType("text")}
            />
          ) : (
            <AiOutlineEyeInvisible
              className="h-[1.5rem] max-h-[1.5rem] min-h-[1.5rem] w-[1.5rem] min-w-[1.5rem] max-w-[1.5rem] cursor-pointer text-icon hover:text-icon-hover transition-colors duration-200"
              onClick={() => setType("password")}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default MkdPasswordInput;
